// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBipSAx7l_EIhUo512bs2Yxih2AOuXWkAA',
    appId: '1:287916907736:web:7604f6cd27ed6dd0d30bad',
    messagingSenderId: '287916907736',
    projectId: 'weather-app-829c1',
    authDomain: 'weather-app-829c1.firebaseapp.com',
    storageBucket: 'weather-app-829c1.firebasestorage.app',
    measurementId: 'G-9RVFKZCRL3',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAfDt2AGGsGZ8Sq8qd5Luwz3l9WHRdObrg',
    appId: '1:287916907736:android:a242d8c9943681fdd30bad',
    messagingSenderId: '287916907736',
    projectId: 'weather-app-829c1',
    storageBucket: 'weather-app-829c1.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCiq5votO-KumJoNKWJb2xu2LN2Nk-mm74',
    appId: '1:287916907736:ios:3b664a8f9d5d226ed30bad',
    messagingSenderId: '287916907736',
    projectId: 'weather-app-829c1',
    storageBucket: 'weather-app-829c1.firebasestorage.app',
    iosBundleId: 'com.example.weatherApp',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyCiq5votO-KumJoNKWJb2xu2LN2Nk-mm74',
    appId: '1:287916907736:ios:3b664a8f9d5d226ed30bad',
    messagingSenderId: '287916907736',
    projectId: 'weather-app-829c1',
    storageBucket: 'weather-app-829c1.firebasestorage.app',
    iosBundleId: 'com.example.weatherApp',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBipSAx7l_EIhUo512bs2Yxih2AOuXWkAA',
    appId: '1:287916907736:web:e3c74723bbd293a3d30bad',
    messagingSenderId: '287916907736',
    projectId: 'weather-app-829c1',
    authDomain: 'weather-app-829c1.firebaseapp.com',
    storageBucket: 'weather-app-829c1.firebasestorage.app',
    measurementId: 'G-MFZH8XM0PT',
  );
}
